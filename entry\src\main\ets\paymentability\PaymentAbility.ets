import { UIAbility, Want } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';
import { BusinessError } from '@kit.BasicServicesKit';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-Payment';

// 定义交易数据接口
export interface TransactionData {
  transactionId: string;
  cardData: string;
  launchSource: string;
}

export default class PaymentAbility extends UIAbility {
  // 存储当前交易数据
  private transactionData: TransactionData = {
    transactionId: '',
    cardData: '',
    launchSource: ''
  };

  onCreate(want: Want): void {
    hilog.info(DOMAIN, TAG, 'PaymentAbility onCreate');
    hilog.info(DOMAIN, TAG, 'Want: %{public}s', JSON.stringify(want));

    // 处理启动参数
    this.handleWantParameters(want);
  }

  onDestroy(): void {
    hilog.info(DOMAIN, TAG, 'PaymentAbility onDestroy');
  }

  onForeground(): void {
    hilog.info(DOMAIN, TAG, 'PaymentAbility onForeground');
  }

  onNewWant(want: Want): void {
    hilog.info(DOMAIN, TAG, 'PaymentAbility onNewWant - 新的刷卡事件');

    // 处理新的启动参数
    this.handleWantParameters(want);

    // 强制触发UI更新 - 添加时间戳触发器
    this.triggerUIUpdate();
  }

  onBackground(): void {
    hilog.info(DOMAIN, TAG, 'PaymentAbility onBackground - 退到后台，关闭PaymentAbility');
    this.context.terminateSelf();
  }

  /**
   * 处理Want参数
   */
  private handleWantParameters(want: Want): void {
    if (want.parameters) {
      const transactionId = want.parameters['transactionId'] as string;
      const launchSource = want.parameters['launchSource'] as string;
      const hceCardData = want.parameters['hceCardData'] as string;

      if (transactionId) {
        AppStorage.setOrCreate('transactionId', transactionId);
        hilog.info(DOMAIN, TAG, 'Set transactionId: %{public}s', transactionId);
      }

      if (launchSource) {
        AppStorage.setOrCreate('launchSource', launchSource);
        hilog.info(DOMAIN, TAG, 'Set launchSource: %{public}s', launchSource);
      }

      if (hceCardData !== undefined) {
        AppStorage.setOrCreate('hceCardData', hceCardData);
        hilog.info(DOMAIN, TAG, 'Set hceCardData: %{public}s', hceCardData || '(empty)');
      }

      // 强制触发UI更新
      hilog.info(DOMAIN, TAG, 'AppStorage updated - triggering UI refresh');
    }
  }

  /**
   * 强制触发UI更新
   */
  private triggerUIUpdate(): void {
    // 使用时间戳触发器强制UI更新
    const timestamp = Date.now().toString();
    AppStorage.setOrCreate('paymentUpdateTrigger', timestamp);
    hilog.info(DOMAIN, TAG, 'UI update triggered with timestamp: %{public}s', timestamp);
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    hilog.info(DOMAIN, TAG, 'onWindowStageCreate');

    windowStage.loadContent('pages/PaymentShowPage', (err: BusinessError | null) => {
      if (err?.code) {
        hilog.error(DOMAIN, TAG, 'Failed to load payment page: %{public}s', err.message || 'Unknown error');
        return;
      }
      hilog.info(DOMAIN, TAG, 'Payment page loaded successfully');
    });
  }

  onWindowStageDestroy(): void {
    hilog.info(DOMAIN, TAG, 'onWindowStageDestroy');
  }

  /**
   * 加载交易数据
   */
  public loadTransactionData(): TransactionData {
    const transactionData: TransactionData = {
      transactionId: AppStorage.get('transactionId') as string || '',
      cardData: AppStorage.get('hceCardData') as string || '',
      launchSource: AppStorage.get('launchSource') as string || ''
    };

    this.transactionData = transactionData;
    return transactionData;
  }

  /**
   * 处理支付操作
   */
  public handlePayment(): void {
    // 获取最新的交易数据
    this.loadTransactionData();

    const transactionId = this.transactionData.transactionId;
    hilog.info(DOMAIN, TAG, `完成支付，交易ID: ${transactionId}`);

    // 执行具体的支付逻辑
    hilog.info(DOMAIN, TAG, '执行支付逻辑');
    // TODO: 实现具体的支付逻辑

    // 关闭PaymentAbility
    this.context.terminateSelf();
    hilog.info(DOMAIN, TAG, 'PaymentAbility closed');
  }
}
