import { hilog } from '@kit.PerformanceAnalysisKit';
import { common, Want } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-PaymentShow';

@Entry
@Component
struct PaymentShowPage {
  @State transactionId: string = '';
  @State cardData: string = '';
  @State launchSource: string = '';
  @State renderTrigger: number = 0; // 用于强制重新渲染
  @StorageLink('paymentUpdateTrigger') @Watch('onUpdateTriggerChanged') updateTrigger: string = '';

  aboutToAppear(): void {
    hilog.info(DOMAIN, TAG, 'PaymentShowPage initialized');

    // 从AppStorage获取初始数据
    this.loadDataFromAppStorage();

    hilog.info(DOMAIN, TAG, `Initial data - transactionId: ${this.transactionId}, cardData: ${this.cardData}, launchSource: ${this.launchSource}`);

    // 确保AppStorage中有初始值
    if (!AppStorage.has('transactionId')) {
      AppStorage.setOrCreate('transactionId', '');
    }
    if (!AppStorage.has('hceCardData')) {
      AppStorage.setOrCreate('hceCardData', '');
    }
    if (!AppStorage.has('launchSource')) {
      AppStorage.setOrCreate('launchSource', '');
    }
    if (!AppStorage.has('paymentUpdateTrigger')) {
      AppStorage.setOrCreate('paymentUpdateTrigger', '');
    }

    // 监听更新触发器变化
    this.watchUpdateTrigger();
  }

  /**
   * 从AppStorage加载数据到本地状态
   */
  private loadDataFromAppStorage(): void {
    this.transactionId = AppStorage.get('transactionId') as string || '';
    this.cardData = AppStorage.get('hceCardData') as string || '';
    this.launchSource = AppStorage.get('launchSource') as string || '';
    this.renderTrigger = Date.now(); // 触发重新渲染

    hilog.info(DOMAIN, TAG, `从AppStorage加载数据 - transactionId: ${this.transactionId}, cardData: ${this.cardData}, launchSource: ${this.launchSource}, renderTrigger: ${this.renderTrigger}`);
  }

  /**
   * 监听更新触发器变化，当有新的刷卡数据时强制刷新界面
   */
  private watchUpdateTrigger(): void {
    // 监听updateTrigger的变化
    // 由于@StorageLink会自动监听AppStorage的变化，我们可以通过监听updateTrigger来触发界面更新
    hilog.info(DOMAIN, TAG, '开始监听支付数据更新');
  }

  /**
   * 当updateTrigger发生变化时，这个方法会被自动调用
   */
  onUpdateTriggerChanged(): void {
    if (this.updateTrigger) {
      hilog.info(DOMAIN, TAG, `检测到数据更新，触发器: ${this.updateTrigger}`);

      // 立即获取最新数据并更新
      const latestTransactionId = AppStorage.get('transactionId') as string || '';
      const latestCardData = AppStorage.get('hceCardData') as string || '';
      const latestLaunchSource = AppStorage.get('launchSource') as string || '';

      hilog.info(DOMAIN, TAG, `从AppStorage获取的最新数据 - transactionId: ${latestTransactionId}, cardData: ${latestCardData}, launchSource: ${latestLaunchSource}`);
      hilog.info(DOMAIN, TAG, `当前界面显示数据 - transactionId: ${this.transactionId}, cardData: ${this.cardData}, launchSource: ${this.launchSource}`);

      // 立即更新本地状态
      if (this.transactionId !== latestTransactionId ||
          this.cardData !== latestCardData ||
          this.launchSource !== latestLaunchSource) {

        hilog.info(DOMAIN, TAG, '立即更新本地状态');
        this.transactionId = latestTransactionId;
        this.cardData = latestCardData;
        this.launchSource = latestLaunchSource;

        // 强制触发重新渲染
        this.renderTrigger = Date.now();

        hilog.info(DOMAIN, TAG, `立即更新完成 - transactionId: ${this.transactionId}, cardData: ${this.cardData}, launchSource: ${this.launchSource}, renderTrigger: ${this.renderTrigger}`);
      }
    }
  }

  /**
   * 强制刷新界面数据
   */
  private forceRefreshData(): void {
    // 重新从AppStorage获取最新数据
    const latestTransactionId = AppStorage.get('transactionId') as string || '';
    const latestCardData = AppStorage.get('hceCardData') as string || '';
    const latestLaunchSource = AppStorage.get('launchSource') as string || '';

    hilog.info(DOMAIN, TAG, `强制刷新数据 - transactionId: ${latestTransactionId}, cardData: ${latestCardData}, launchSource: ${latestLaunchSource}`);

    // 检查数据是否有变化
    if (this.transactionId !== latestTransactionId ||
        this.cardData !== latestCardData ||
        this.launchSource !== latestLaunchSource) {

      hilog.info(DOMAIN, TAG, '检测到数据不一致，直接更新本地状态');
      hilog.info(DOMAIN, TAG, `更新前 - transactionId: ${this.transactionId}, cardData: ${this.cardData}, launchSource: ${this.launchSource}`);

      // 使用异步更新确保界面重新渲染
      setTimeout(() => {
        this.transactionId = latestTransactionId;
        this.cardData = latestCardData;
        this.launchSource = latestLaunchSource;

        hilog.info(DOMAIN, TAG, `异步更新后 - transactionId: ${this.transactionId}, cardData: ${this.cardData}, launchSource: ${this.launchSource}`);

        // 强制触发界面重新渲染
        this.forceUIUpdate();
      }, 0);
    } else {
      hilog.info(DOMAIN, TAG, '界面数据已经是最新的');
    }
  }

  /**
   * 强制触发界面重新渲染
   */
  private forceUIUpdate(): void {
    // 通过修改renderTrigger来强制重新渲染
    this.renderTrigger = Date.now();
    hilog.info(DOMAIN, TAG, `强制UI更新，渲染触发器: ${this.renderTrigger}`);
  }

  /**
   * 页面显示时的回调
   */
  onPageShow(): void {
    hilog.info(DOMAIN, TAG, 'PaymentShowPage onPageShow');

    // 页面显示时重新加载数据
    this.loadDataFromAppStorage();

    hilog.info(DOMAIN, TAG, `Current data - transactionId: ${this.transactionId}, cardData: ${this.cardData}, launchSource: ${this.launchSource}`);
  }

  /**
   * 页面隐藏时的回调
   */
  onPageHide(): void {
    hilog.info(DOMAIN, TAG, 'PaymentShowPage onPageHide');
  }

  /**
   * 页面标题组件
   */
  @Builder
  HeaderSection() {
    Text('支付确认')
      .fontSize(24)
      .fontWeight(FontWeight.Bold)
  }

  /**
   * 操作按钮组件
   */
  @Builder
  ActionButtonsSection() {
    Button('完成')
      .width('90%')
      .height(48)
      .backgroundColor('#007AFF')
      .borderRadius(24)
      .fontColor(Color.White)
      .fontSize(16)
      .fontWeight(FontWeight.Medium)
      .onClick(() => {
        this.handlePaymentComplete();
      })
  }

  /**
   * 处理支付完成逻辑
   */
  private handlePaymentComplete(): void {
    hilog.info(DOMAIN, TAG, '支付完成，准备返回主应用');

    try {
      const context = getContext(this) as common.UIAbilityContext;

      // 启动主应用的EntryAbility
      const want: Want = {
        bundleName: context.abilityInfo.bundleName,
        abilityName: 'EntryAbility',
        moduleName: context.abilityInfo.moduleName || "entry",
        parameters: {
          returnFromPayment: 'true',
          paymentResult: 'completed'
        }
      };

      context.startAbility(want).then(() => {
        hilog.info(DOMAIN, TAG, '成功启动主应用');
        // 启动主应用后关闭PaymentAbility
        context.terminateSelf();
      }).catch((err: BusinessError) => {
        hilog.error(DOMAIN, TAG, '启动主应用失败: %{public}s', JSON.stringify(err));
        // 如果启动失败，直接关闭PaymentAbility
        context.terminateSelf();
      });
    } catch (error) {
      hilog.error(DOMAIN, TAG, '处理支付完成时发生异常: %{public}s', JSON.stringify(error));
      // 发生异常时直接关闭PaymentAbility
      const context = getContext(this) as common.UIAbilityContext;
      context.terminateSelf();
    }
  }

  /**
   * 信息项组件
   */
  @Builder
  InfoItem(label: string, value: string) {
    Text(`${label}: ${value}`)
      .fontSize(16)
      .alignSelf(ItemAlign.Start)
  }


  /**
   * 通用按钮组件
   */
  @Builder
  ActionButton(text: string, bgColor: string, onClick: () => void) {
    Button(text)
      .width('40%')
      .height(50)
      .backgroundColor(bgColor)
      .onClick(onClick)
  }

  /**
   * 交易信息展示组件
   */
  @Builder
  TransactionInfoSection() {
    Column({ space: 16 }) {
      this.InfoItem('交易ID', this.transactionId || '未知')
      this.InfoItem('卡片数据', this.cardData || '无')
      this.InfoItem('启动来源', this.launchSource || '未知')
      this.InfoItem('更新触发器', this.renderTrigger.toString())
    }
    .alignItems(HorizontalAlign.Start)
    .width('100%')
    .padding(20)
    .backgroundColor('#f5f5f5')
    .borderRadius(10)
  }

  build() {
    Column() {
      // 隐藏的渲染触发器元素（用于强制重新渲染）
      Text(`${this.renderTrigger}`)
        .fontSize(0)
        .height(0)
        .opacity(0)

      // 上部内容区域
      Column({ space: 24 }) {
        this.HeaderSection()
        this.TransactionInfoSection()
      }
      .layoutWeight(1)
      .width('100%')

      // 底部按钮区域
      Column() {
        this.ActionButtonsSection()
      }
      .width('100%')
      .padding({ bottom: 40 })
    }
    .width('100%')
    .height('100%')
    .padding(20)
    .backgroundColor($r('sys.color.white'))
    .justifyContent(FlexAlign.SpaceBetween)
  }
}
