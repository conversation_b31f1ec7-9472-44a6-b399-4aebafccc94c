import { hilog } from '@kit.PerformanceAnalysisKit';
import { common, Want } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';

// 支付数据接口
interface PaymentUpdateData {
  transactionId: string;
  cardData: string;
  launchSource: string;
  updateTime: number;
}

// 支付数据模型
@Observed
class PaymentData {
  @Track transactionId: string = '';
  @Track cardData: string = '';
  @Track launchSource: string = '';
  @Track updateTime: number = 0;

  constructor(transactionId: string = '', cardData: string = '', launchSource: string = '') {
    this.transactionId = transactionId;
    this.cardData = cardData;
    this.launchSource = launchSource;
    this.updateTime = Date.now();
  }

  updateData(transactionId: string, cardData: string, launchSource: string): void {
    this.transactionId = transactionId;
    this.cardData = cardData;
    this.launchSource = launchSource;
    this.updateTime = Date.now();
  }
}

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-PaymentShow';

@Entry
@Component
struct PaymentShowPage {
  // 使用最基本的 @State 变量
  @State transactionId: string = '';
  @State cardData: string = '';
  @State launchSource: string = '';
  @State updateTime: number = 0;
  @State forceUpdateCounter: number = 0; // 强制更新计数器

  private context = getContext(this) as common.UIAbilityContext;

  aboutToAppear(): void {
    hilog.info(DOMAIN, TAG, 'PaymentShowPage initialized');

    // 从AppStorage获取初始数据
    this.loadDataFromAppStorage();

    hilog.info(DOMAIN, TAG, `Initial data - transactionId: ${this.transactionId}, cardData: ${this.cardData}, launchSource: ${this.launchSource}`);

    // 确保AppStorage中有初始值
    if (!AppStorage.has('transactionId')) {
      AppStorage.setOrCreate('transactionId', '');
    }
    if (!AppStorage.has('hceCardData')) {
      AppStorage.setOrCreate('hceCardData', '');
    }
    if (!AppStorage.has('launchSource')) {
      AppStorage.setOrCreate('launchSource', '');
    }
    if (!AppStorage.has('paymentUpdateTrigger')) {
      AppStorage.setOrCreate('paymentUpdateTrigger', '');
    }

    // 订阅EventHub事件
    this.subscribeToPaymentUpdates();
  }

  /**
   * 从AppStorage加载数据到本地状态
   */
  private loadDataFromAppStorage(): void {
    this.transactionId = AppStorage.get('transactionId') as string || '';
    this.cardData = AppStorage.get('hceCardData') as string || '';
    this.launchSource = AppStorage.get('launchSource') as string || '';
    this.updateTime = Date.now();

    hilog.info(DOMAIN, TAG, `从AppStorage加载数据 - transactionId: ${this.transactionId}, cardData: ${this.cardData}, launchSource: ${this.launchSource}, updateTime: ${this.updateTime}`);
  }

  /**
   * 订阅EventHub支付数据更新事件
   */
  private subscribeToPaymentUpdates(): void {
    try {
      hilog.info(DOMAIN, TAG, '开始订阅EventHub支付数据更新事件');

      // 订阅支付数据更新事件
      this.context.eventHub.on('paymentDataUpdate', (data: PaymentUpdateData) => {
        hilog.info(DOMAIN, TAG, 'EventHub接收到支付数据更新: %{public}s', JSON.stringify(data));

        // 直接更新界面数据
        this.updatePaymentData(data);
      });

      hilog.info(DOMAIN, TAG, 'EventHub订阅成功');

    } catch (error) {
      hilog.error(DOMAIN, TAG, 'EventHub订阅失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 更新支付数据
   */
  private updatePaymentData(data: PaymentUpdateData): void {
    hilog.info(DOMAIN, TAG, '开始更新支付数据');
    hilog.info(DOMAIN, TAG, `更新前 - transactionId: ${this.transactionId}, cardData: ${this.cardData}, launchSource: ${this.launchSource}`);

    // 直接更新 @State 变量
    this.transactionId = data.transactionId;
    this.cardData = data.cardData;
    this.launchSource = data.launchSource;
    this.updateTime = data.updateTime;
    this.forceUpdateCounter++;

    hilog.info(DOMAIN, TAG, `更新后 - transactionId: ${this.transactionId}, cardData: ${this.cardData}, launchSource: ${this.launchSource}, updateTime: ${this.updateTime}, counter: ${this.forceUpdateCounter}`);
  }

  /**
   * 强制触发界面重新构建
   */
  private forceUIRebuild(): void {
    try {
      // 方法1: 尝试通过修改组件状态强制重建
      hilog.info(DOMAIN, TAG, '尝试强制界面重新构建');

      // 方法2: 使用 setTimeout 确保在下一个事件循环中更新
      setTimeout(() => {
        hilog.info(DOMAIN, TAG, '延迟强制更新界面');
        // 这里可以尝试其他强制更新的方法
      }, 0);

    } catch (error) {
      hilog.error(DOMAIN, TAG, '强制界面重建失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 页面隐藏时的回调
   */
  onPageHide(): void {
    hilog.info(DOMAIN, TAG, 'PaymentShowPage onPageHide');

    // 清理定时器
    if (this.pollingTimer !== -1) {
      clearInterval(this.pollingTimer);
      this.pollingTimer = -1;
      hilog.info(DOMAIN, TAG, '清理定时器');
    }
  }

  /**
   * 页面即将销毁时的回调
   */
  aboutToDisappear(): void {
    hilog.info(DOMAIN, TAG, 'PaymentShowPage aboutToDisappear');

    // 确保清理定时器
    if (this.pollingTimer !== -1) {
      clearInterval(this.pollingTimer);
      this.pollingTimer = -1;
      hilog.info(DOMAIN, TAG, '确保清理定时器');
    }
  }



  /**
   * 页面显示时的回调
   */
  onPageShow(): void {
    hilog.info(DOMAIN, TAG, 'PaymentShowPage onPageShow');

    // 页面显示时重新加载数据
    this.loadDataFromAppStorage();

    hilog.info(DOMAIN, TAG, `Current data - transactionId: ${this.transactionId}, cardData: ${this.cardData}, launchSource: ${this.launchSource}`);
  }



  /**
   * 页面标题组件
   */
  @Builder
  HeaderSection() {
    Text('支付确认')
      .fontSize(24)
      .fontWeight(FontWeight.Bold)
  }

  /**
   * 操作按钮组件
   */
  @Builder
  ActionButtonsSection() {
    Button('完成')
      .width('90%')
      .height(48)
      .backgroundColor('#007AFF')
      .borderRadius(24)
      .fontColor(Color.White)
      .fontSize(16)
      .fontWeight(FontWeight.Medium)
      .onClick(() => {
        this.handlePaymentComplete();
      })
  }

  /**
   * 处理支付完成逻辑
   */
  private handlePaymentComplete(): void {
    hilog.info(DOMAIN, TAG, '支付完成，准备返回主应用');

    try {
      const context = getContext(this) as common.UIAbilityContext;

      // 启动主应用的EntryAbility
      const want: Want = {
        bundleName: context.abilityInfo.bundleName,
        abilityName: 'EntryAbility',
        moduleName: context.abilityInfo.moduleName || "entry",
        parameters: {
          returnFromPayment: 'true',
          paymentResult: 'completed'
        }
      };

      context.startAbility(want).then(() => {
        hilog.info(DOMAIN, TAG, '成功启动主应用');
        // 启动主应用后关闭PaymentAbility
        context.terminateSelf();
      }).catch((err: BusinessError) => {
        hilog.error(DOMAIN, TAG, '启动主应用失败: %{public}s', JSON.stringify(err));
        // 如果启动失败，直接关闭PaymentAbility
        context.terminateSelf();
      });
    } catch (error) {
      hilog.error(DOMAIN, TAG, '处理支付完成时发生异常: %{public}s', JSON.stringify(error));
      // 发生异常时直接关闭PaymentAbility
      const context = getContext(this) as common.UIAbilityContext;
      context.terminateSelf();
    }
  }

  /**
   * 信息项组件
   */
  @Builder
  InfoItem(label: string, value: string) {
    Text(`${label}: ${value}`)
      .fontSize(16)
      .alignSelf(ItemAlign.Start)
  }


  /**
   * 通用按钮组件
   */
  @Builder
  ActionButton(text: string, bgColor: string, onClick: () => void) {
    Button(text)
      .width('40%')
      .height(50)
      .backgroundColor(bgColor)
      .onClick(onClick)
  }

  /**
   * 交易信息展示组件
   */
  @Builder
  TransactionInfoSection() {
    Column({ space: 16 }) {
      this.InfoItem('交易ID', this.transactionId || '未知')
      this.InfoItem('卡片数据', this.cardData || '无')
      this.InfoItem('启动来源', this.launchSource || '未知')
      this.InfoItem('更新时间', this.updateTime.toString())
      this.InfoItem('更新计数器', this.forceUpdateCounter.toString())
    }
    .alignItems(HorizontalAlign.Start)
    .width('100%')
    .padding(20)
    .backgroundColor('#f5f5f5')
    .borderRadius(10)
  }

  build() {
    Column() {
      // 上部内容区域
      Column({ space: 24 }) {
        this.HeaderSection()
        this.TransactionInfoSection()
      }
      .layoutWeight(1)
      .width('100%')
      .key(`content-${this.forceUpdateCounter}`) // 使用计数器作为key强制重新渲染

      // 底部按钮区域
      Column() {
        this.ActionButtonsSection()
      }
      .width('100%')
      .padding({ bottom: 40 })
    }
    .width('100%')
    .height('100%')
    .padding(20)
    .backgroundColor($r('sys.color.white'))
    .justifyContent(FlexAlign.SpaceBetween)
    .key(`main-${this.forceUpdateCounter}`) // 主容器也使用key
  }
}
